{"name": "gyro-ball", "power": null, "accuracy": 100, "pp": 5, "type": "steel", "damage_class": "physical", "effect_entries": [{"effect": "Inflicts regular damage.  Power increases with the target's current Speed compared to the user, given by `1 + 25 * target Speed / user Speed`, capped at 150.", "language": {"name": "en", "url": "https://pokeapi.co/api/v2/language/9/"}, "short_effect": "Power raises when the user has lower Speed, up to a maximum of 150."}]}