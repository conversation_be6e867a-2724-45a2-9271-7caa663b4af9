{"name": "trump-card", "power": null, "accuracy": null, "pp": 5, "type": "normal", "damage_class": "special", "effect_entries": [{"effect": "Inflicts regular damage.  Power is determined by the PP remaining for this move, after its PP cost is deducted.  Ignores accuracy and evasion modifiers.\n\nPP remaining | Power\n------------ | ----:\n4 or more    |    40\n3            |    50\n2            |    60\n1            |    80\n0            |   200\n\nIf this move is activated by another move, the activating move's PP is used to calculate power.", "language": {"name": "en", "url": "https://pokeapi.co/api/v2/language/9/"}, "short_effect": "Power increases when this move has less PP, up to a maximum of 200."}]}