{"version": "1.0.0", "generatedAt": "2025-07-22T13:25:48.790Z", "specialMoves": [{"name": "night-shade", "power": null, "damage_class": "special", "effects": [{"pattern": "damage.*equal.*to.*user'?s.*level", "match": "damage equal to the user's level.  type immunity applies, but other type effects are ignored. inflicts damage equal to the user's level", "fullMatch": ["damage equal to the user's level.  type immunity applies, but other type effects are ignored. inflicts damage equal to the user's level"]}, {"pattern": "inflicts.*damage.*equal.*to.*the.*user'?s.*level", "match": "inflicts damage equal to the user's level.  type immunity applies, but other type effects are ignored. inflicts damage equal to the user's level", "fullMatch": ["inflicts damage equal to the user's level.  type immunity applies, but other type effects are ignored. inflicts damage equal to the user's level"]}, {"pattern": "zero_power_damage_move", "match": "Zero power move with damage effect", "fullMatch": ["zero power damage"]}], "effect_text": "Inflicts damage equal to the user's level.  Type immunity applies, but other type effects are ignored.", "short_effect": "Inflicts damage equal to the user's level."}, {"name": "mimic", "power": null, "damage_class": "status", "effects": [{"pattern": "mirror.*move", "match": "mirror move, nor selected by assist or metronome, nor forced by encore. copies the target's last used move", "fullMatch": ["mirror move, nor selected by assist or metronome, nor forced by encore. copies the target's last used move"]}], "effect_text": "This move is replaced by the target's last successfully used move, and its PP changes to 5.  If the target hasn't used a move since entering the field, if it tried to use a move this turn and failed, or if the user already knows the targeted move, this move will fail.  This effect vanishes when the user leaves the field.\n\nIf chatter, metronome, mimic, sketch, or struggle is selected, this move will fail.\n\nThis move cannot be copied by mirror move, nor selected by assist or metronome, nor forced by encore.", "short_effect": "Copies the target's last used move."}, {"name": "light-screen", "power": null, "damage_class": "status", "effects": [{"pattern": "status_move_with_damage", "match": "Status move that deals damage", "fullMatch": ["status move with damage"]}, {"pattern": "zero_power_damage_move", "match": "Zero power move with damage effect", "fullMatch": ["zero power damage"]}], "effect_text": "Erects a barrier around the user's side of the field that reduces damage from special attacks by half for five turns.  In double battles, the reduction is 1/3.  Critical hits are not affected by the barrier.\n\nIf the user is holding light clay, the barrier lasts for eight turns.\n\nbrick break or defog used by an opponent will destroy the barrier.", "short_effect": "Reduces damage from special attacks by 50% for five turns."}, {"name": "reflect", "power": null, "damage_class": "status", "effects": [{"pattern": "status_move_with_damage", "match": "Status move that deals damage", "fullMatch": ["status move with damage"]}, {"pattern": "zero_power_damage_move", "match": "Zero power move with damage effect", "fullMatch": ["zero power damage"]}], "effect_text": "Erects a barrier around the user's side of the field that reduces damage from physical attacks by half for five turns.  In double battles, the reduction is 1/3.  Critical hits are not affected by the barrier.\n\nIf the user is holding light clay, the barrier lasts for eight turns.\n\nbrick break or defog used by an opponent will destroy the barrier.", "short_effect": "Reduces damage from physical attacks by half."}, {"name": "bide", "power": null, "damage_class": "physical", "effects": [{"pattern": "inflicts.*twice.*the.*damage", "match": "inflicts twice the damage it accumulated on the last pokémon to hit it.  damage", "fullMatch": ["inflicts twice the damage it accumulated on the last pokémon to hit it.  damage"]}, {"pattern": "zero_power_damage_move", "match": "Zero power move with damage effect", "fullMatch": ["zero power damage"]}], "effect_text": "User waits for two turns.  On the second turn, the user inflicts twice the damage it accumulated on the last Pokémon to hit it.  Damage inflicted is typeless.\n\nThis move cannot be selected by sleep talk.", "short_effect": "User waits for two turns, then hits back for twice the damage it took."}, {"name": "metronome", "power": null, "damage_class": "status", "effects": [{"pattern": "mirror.*move", "match": "mirror coat, mirror move, protect, quick guard, sketch, sleep talk, snatch, struggle, switcheroo, thief, trick, and wide guard will not be selected by this move", "fullMatch": ["mirror coat, mirror move, protect, quick guard, sketch, sleep talk, snatch, struggle, switcheroo, thief, trick, and wide guard will not be selected by this move"]}], "effect_text": "Selects any move at random and uses it.  Moves the user already knows are not eligible.  Assist, meta, protection, and reflection moves are also not eligible; specifically, assist, chatter, copycat, counter, covet, destiny bond, detect, endure, feint, focus punch, follow me, helping hand, me first, metronome, mimic, mirror coat, mirror move, protect, quick guard, sketch, sleep talk, snatch, struggle, switcheroo, thief, trick, and wide guard will not be selected by this move.\n\nThis move cannot be copied by mimic or mirror move, nor selected by assist, metronome, or sleep talk.", "short_effect": "Randomly selects and uses any move in the game."}, {"name": "mirror-move", "power": null, "damage_class": "status", "effects": [{"pattern": "mirror.*move", "match": "mirror coat, mirror move", "fullMatch": ["mirror coat, mirror move"]}], "effect_text": "Uses the last move targeted at the user by a Pokémon still on the field.  A move counts as targeting the user even if it hit multiple Pokémon, as long as the user was one of them; however, moves targeting the field itself do not count.  If the user has not been targeted by an appropriate move since entering the field, or if no Pokémon that targeted the user remains on the field, this move will fail.\n\nMoves that failed, missed, had no effect, or were blocked are still copied.\n\nAssist moves, time-delayed moves, “meta” moves that operate on other moves/Pokémon/abilities, and some other special moves cannot be copied and are ignored; if the last move to hit the user was such a move, the previous move will be used instead.  The full list of ignored moves is: acid armor, acupressure, after you, agility, ally switch, amnesia, aqua ring, aromatherapy, aromatic mist, assist, autotomize, barrier, baton pass, belch, belly drum, bide, bulk up, calm mind, camouflage, celebrate, charge, coil, conversion, conversion 2, copycat, cosmic power, cotton guard, counter, crafty shield, curse, defend order, defense curl, destiny bond, detect, doom desire, double team, dragon dance, electric terrain, endure, final gambit, flower shield, focus energy, focus punch, follow me, future sight, geomancy, grassy terrain, gravity, growth, grudge, guard split, hail, happy hour, harden, haze, heal bell, heal order, heal pulse, healing wish, helping hand, hold hands, hone claws, howl, imprison, ingrain, ion deluge, iron defense, kings shield, light screen, lucky chant, lunar dance, magic coat, magnet rise, magnetic flux, mat block, me first, meditate, metronome, milk drink, mimic, minimize, mirror coat, mirror move, mist, misty terrain, moonlight, morning sun, mud sport, nasty plot, nature power, perish song, power split, power trick, protect, psych up, quick guard, quiver dance, rage powder, rain dance, recover, recycle, reflect, reflect type, refresh, rest, rock polish, role play, roost, rototiller, safeguard, sandstorm, shadow blast, shadow bolt, shadow half, shadow rush, shadow shed, shadow sky, shadow storm, shadow wave, sharpen, shell smash, shift gear, sketch, slack off, sleep talk, snatch, soft boiled, spikes, spiky shield, spit up, splash, stealth rock, sticky web, stockpile, struggle, substitute, sunny day, swallow, swords dance, synthesis, tail glow, tailwind, teleport, toxic spikes, transform, water sport, wide guard, wish, withdraw and work up.\n\nThis move cannot be selected by assist, metronome, or sleep talk, nor forced by encore.", "short_effect": "Uses the target's last used move."}, {"name": "guillotine", "power": null, "damage_class": "physical", "effects": [{"pattern": "inflicts.*damage.*equal.*to.*(\\d+)", "match": "inflicts damage equal to the target's max hp.  ignores accuracy and evasion modifiers.  this move's accuracy is 30% plus 1", "fullMatch": ["inflicts damage equal to the target's max hp.  ignores accuracy and evasion modifiers.  this move's accuracy is 30% plus 1", "1"]}, {"pattern": "one.*hit.*ko", "match": "one-hit ko", "fullMatch": ["one-hit ko"]}, {"pattern": "ignores.*accuracy.*and.*evasion", "match": "ignores accuracy and evasion", "fullMatch": ["ignores accuracy and evasion"]}, {"pattern": "zero_power_damage_move", "match": "Zero power move with damage effect", "fullMatch": ["zero power damage"]}], "effect_text": "Inflicts damage equal to the target's max HP.  Ignores accuracy and evasion modifiers.  This move's accuracy is 30% plus 1% for each level the user is higher than the target.  If the user is a lower level than the target, this move will fail.\n\nBecause this move inflicts a specific and finite amount of damage, endure still prevents the target from fainting.\n\nThe effects of lock on, mind reader, and no guard still apply, as long as the user is equal or higher level than the target.  However, they will not give this move a chance to break through detect or protect.", "short_effect": "Causes a one-hit KO."}, {"name": "swift", "power": 60, "damage_class": "special", "effects": [{"pattern": "ignores.*accuracy.*and.*evasion", "match": "ignores accuracy and evasion", "fullMatch": ["ignores accuracy and evasion"]}], "effect_text": "Inflicts regular damage.  Ignores accuracy and evasion modifiers.", "short_effect": "Never misses."}, {"name": "transform", "power": null, "damage_class": "status", "effects": [{"pattern": "mirror.*move", "match": "mirror move", "fullMatch": ["mirror move"]}], "effect_text": "User copies the target's species, weight, type, ability, calculated stats (except HP), and moves.  Copied moves will all have 5 PP remaining.  IVs are copied for the purposes of hidden power, but stats are not recalculated.\n\nchoice band, choice scarf, and choice specs stay in effect, and the user must select a new move.\n\nThis move cannot be copied by mirror move, nor forced by encore.", "short_effect": "User becomes a copy of the target until it leaves battle."}, {"name": "flail", "power": null, "damage_class": "physical", "effects": [{"pattern": "damage.*varies", "match": "damage.  power varies", "fullMatch": ["damage.  power varies"]}, {"pattern": "zero_power_damage_move", "match": "Zero power move with damage effect", "fullMatch": ["zero power damage"]}], "effect_text": "Inflicts regular damage.  Power varies inversely with the user's proportional remaining HP.\n\n64 * current HP / max HP | Power\n-----------------------: | ----:\n 0– 1                    |  200\n 2– 5                    |  150\n 6–12                    |  100\n13–21                    |   80\n22–42                    |   40\n43–64                    |   20\n", "short_effect": "Inflicts more damage when the user has less HP remaining, with a maximum of 200 power."}, {"name": "bind", "power": 15, "damage_class": "physical", "effects": [{"pattern": "inflicts.*(\\d+).*damage", "match": "inflicts regular damage.  for the next 2–5 turns, the target cannot leave the field and is damage", "fullMatch": ["inflicts regular damage.  for the next 2–5 turns, the target cannot leave the field and is damage", "5"]}], "effect_text": "Inflicts regular damage.  For the next 2–5 turns, the target cannot leave the field and is damaged for 1/16 its max HP at the end of each turn.  The user continues to use other moves during this time.  If the user leaves the field, this effect ends.\n\nHas a 3/8 chance each to hit 2 or 3 times, and a 1/8 chance each to hit 4 or 5 times.  Averages to 3 hits per use.\n\nrapid spin cancels this effect.", "short_effect": "Prevents the target from fleeing and inflicts damage for 2-5 turns."}, {"name": "horn-drill", "power": null, "damage_class": "physical", "effects": [{"pattern": "inflicts.*damage.*equal.*to.*(\\d+)", "match": "inflicts damage equal to the target's max hp.  ignores accuracy and evasion modifiers.  this move's accuracy is 30% plus 1", "fullMatch": ["inflicts damage equal to the target's max hp.  ignores accuracy and evasion modifiers.  this move's accuracy is 30% plus 1", "1"]}, {"pattern": "one.*hit.*ko", "match": "one-hit ko", "fullMatch": ["one-hit ko"]}, {"pattern": "ignores.*accuracy.*and.*evasion", "match": "ignores accuracy and evasion", "fullMatch": ["ignores accuracy and evasion"]}, {"pattern": "zero_power_damage_move", "match": "Zero power move with damage effect", "fullMatch": ["zero power damage"]}], "effect_text": "Inflicts damage equal to the target's max HP.  Ignores accuracy and evasion modifiers.  This move's accuracy is 30% plus 1% for each level the user is higher than the target.  If the user is a lower level than the target, this move will fail.\n\nBecause this move inflicts a specific and finite amount of damage, endure still prevents the target from fainting.\n\nThe effects of lock on, mind reader, and no guard still apply, as long as the user is equal or higher level than the target.  However, they will not give this move a chance to break through detect or protect.", "short_effect": "Causes a one-hit KO."}, {"name": "wrap", "power": 15, "damage_class": "physical", "effects": [{"pattern": "inflicts.*(\\d+).*damage", "match": "inflicts regular damage.  for the next 2–5 turns, the target cannot leave the field and is damage", "fullMatch": ["inflicts regular damage.  for the next 2–5 turns, the target cannot leave the field and is damage", "5"]}], "effect_text": "Inflicts regular damage.  For the next 2–5 turns, the target cannot leave the field and is damaged for 1/16 its max HP at the end of each turn.  The user continues to use other moves during this time.  If the user leaves the field, this effect ends.\n\nHas a 3/8 chance each to hit 2 or 3 times, and a 1/8 chance each to hit 4 or 5 times.  Averages to 3 hits per use.\n\nrapid spin cancels this effect.", "short_effect": "Prevents the target from fleeing and inflicts damage for 2-5 turns."}, {"name": "take-down", "power": 90, "damage_class": "physical", "effects": [{"pattern": "inflicts.*(\\d+).*damage", "match": "inflicts regular damage.  user takes 1/4 the damage it inflicts in recoil. user receives 1/4 the damage", "fullMatch": ["inflicts regular damage.  user takes 1/4 the damage it inflicts in recoil. user receives 1/4 the damage", "4"]}], "effect_text": "Inflicts regular damage.  User takes 1/4 the damage it inflicts in recoil.", "short_effect": "User receives 1/4 the damage it inflicts in recoil."}, {"name": "double-edge", "power": 120, "damage_class": "physical", "effects": [{"pattern": "inflicts.*(\\d+).*damage", "match": "inflicts regular damage.  user takes 1/3 the damage it inflicts in recoil. user receives 1/3 the damage", "fullMatch": ["inflicts regular damage.  user takes 1/3 the damage it inflicts in recoil. user receives 1/3 the damage", "3"]}], "effect_text": "Inflicts regular damage.  User takes 1/3 the damage it inflicts in recoil.", "short_effect": "User receives 1/3 the damage inflicted in recoil."}, {"name": "sonic-boom", "power": null, "damage_class": "special", "effects": [{"pattern": "inflicts.*(\\d+).*damage", "match": "inflicts exactly 20 damage. inflicts 20 points of damage", "fullMatch": ["inflicts exactly 20 damage. inflicts 20 points of damage", "0"]}, {"pattern": "zero_power_damage_move", "match": "Zero power move with damage effect", "fullMatch": ["zero power damage"]}], "effect_text": "Inflicts exactly 20 damage.", "short_effect": "Inflicts 20 points of damage."}, {"name": "submission", "power": 80, "damage_class": "physical", "effects": [{"pattern": "inflicts.*(\\d+).*damage", "match": "inflicts regular damage.  user takes 1/4 the damage it inflicts in recoil. user receives 1/4 the damage", "fullMatch": ["inflicts regular damage.  user takes 1/4 the damage it inflicts in recoil. user receives 1/4 the damage", "4"]}], "effect_text": "Inflicts regular damage.  User takes 1/4 the damage it inflicts in recoil.", "short_effect": "User receives 1/4 the damage it inflicts in recoil."}, {"name": "low-kick", "power": null, "damage_class": "physical", "effects": [{"pattern": "zero_power_damage_move", "match": "Zero power move with damage effect", "fullMatch": ["zero power damage"]}], "effect_text": "Inflicts regular damage.  Power increases with the target's weight in kilograms, to a maximum of 120.\n\nTarget's weight | Power\n--------------- | ----:\nUp to 10kg      |    20\nUp to 25kg      |    40\nUp to 50kg      |    60\nUp to 100kg     |    80\nUp to 200kg     |   100\nAbove 200kg     |   120\n", "short_effect": "Inflicts more damage to heavier targets, with a maximum of 120 power."}, {"name": "counter", "power": null, "damage_class": "physical", "effects": [{"pattern": "inflicts.*twice.*the.*damage", "match": "inflicts twice the damage", "fullMatch": ["inflicts twice the damage"]}, {"pattern": "mirror.*move", "match": "mirror move", "fullMatch": ["mirror move"]}, {"pattern": "zero_power_damage_move", "match": "Zero power move with damage effect", "fullMatch": ["zero power damage"]}], "effect_text": "Targets the last opposing Pokémon to hit the user with a physical move this turn.  Inflicts twice the damage that move did to the user.  If there is no eligible target, this move will fail.  Type immunity applies, but other type effects are ignored.\n\nThis move cannot be copied by mirror move, nor selected by assist or metronome.", "short_effect": "Inflicts twice the damage the user received from the last physical hit it took."}, {"name": "seismic-toss", "power": null, "damage_class": "physical", "effects": [{"pattern": "damage.*equal.*to.*user'?s.*level", "match": "damage equal to the user's level.  type immunity applies, but other type effects are ignored. inflicts damage equal to the user's level", "fullMatch": ["damage equal to the user's level.  type immunity applies, but other type effects are ignored. inflicts damage equal to the user's level"]}, {"pattern": "inflicts.*damage.*equal.*to.*the.*user'?s.*level", "match": "inflicts damage equal to the user's level.  type immunity applies, but other type effects are ignored. inflicts damage equal to the user's level", "fullMatch": ["inflicts damage equal to the user's level.  type immunity applies, but other type effects are ignored. inflicts damage equal to the user's level"]}, {"pattern": "zero_power_damage_move", "match": "Zero power move with damage effect", "fullMatch": ["zero power damage"]}], "effect_text": "Inflicts damage equal to the user's level.  Type immunity applies, but other type effects are ignored.", "short_effect": "Inflicts damage equal to the user's level."}, {"name": "leech-seed", "power": null, "damage_class": "status", "effects": [{"pattern": "status_move_with_damage", "match": "Status move that deals damage", "fullMatch": ["status move with damage"]}, {"pattern": "zero_power_damage_move", "match": "Zero power move with damage effect", "fullMatch": ["zero power damage"]}], "effect_text": "Plants a seed on the target that drains 1/8 of its max HP at the end of every turn and heals the user for the amount taken.  Has no effect on grass Pokémon.  The seed remains until the target leaves the field.\n\nThe user takes damage instead of being healed if the target has liquid ooze.\n\nrapid spin will remove this effect.\n\nThis effect is passed on by baton pass.", "short_effect": "Seeds the target, stealing HP from it every turn."}, {"name": "dragon-rage", "power": null, "damage_class": "special", "effects": [{"pattern": "inflicts.*(\\d+).*damage", "match": "inflicts exactly 40 damage. inflicts 40 points of damage", "fullMatch": ["inflicts exactly 40 damage. inflicts 40 points of damage", "0"]}, {"pattern": "zero_power_damage_move", "match": "Zero power move with damage effect", "fullMatch": ["zero power damage"]}], "effect_text": "Inflicts exactly 40 damage.", "short_effect": "Inflicts 40 points of damage."}, {"name": "fire-spin", "power": 35, "damage_class": "special", "effects": [{"pattern": "inflicts.*(\\d+).*damage", "match": "inflicts regular damage.  for the next 2–5 turns, the target cannot leave the field and is damage", "fullMatch": ["inflicts regular damage.  for the next 2–5 turns, the target cannot leave the field and is damage", "5"]}], "effect_text": "Inflicts regular damage.  For the next 2–5 turns, the target cannot leave the field and is damaged for 1/16 its max HP at the end of each turn.  The user continues to use other moves during this time.  If the user leaves the field, this effect ends.\n\nHas a 3/8 chance each to hit 2 or 3 times, and a 1/8 chance each to hit 4 or 5 times.  Averages to 3 hits per use.\n\nrapid spin cancels this effect.", "short_effect": "Prevents the target from fleeing and inflicts damage for 2-5 turns."}, {"name": "fissure", "power": null, "damage_class": "physical", "effects": [{"pattern": "inflicts.*damage.*equal.*to.*(\\d+)", "match": "inflicts damage equal to the target's max hp.  ignores accuracy and evasion modifiers.  this move's accuracy is 30% plus 1", "fullMatch": ["inflicts damage equal to the target's max hp.  ignores accuracy and evasion modifiers.  this move's accuracy is 30% plus 1", "1"]}, {"pattern": "one.*hit.*ko", "match": "one-hit ko", "fullMatch": ["one-hit ko"]}, {"pattern": "ignores.*accuracy.*and.*evasion", "match": "ignores accuracy and evasion", "fullMatch": ["ignores accuracy and evasion"]}, {"pattern": "zero_power_damage_move", "match": "Zero power move with damage effect", "fullMatch": ["zero power damage"]}], "effect_text": "Inflicts damage equal to the target's max HP.  Ignores accuracy and evasion modifiers.  This move's accuracy is 30% plus 1% for each level the user is higher than the target.  If the user is a lower level than the target, this move will fail.\n\nBecause this move inflicts a specific and finite amount of damage, endure still prevents the target from fainting.\n\nThe effects of lock on, mind reader, and no guard still apply, as long as the user is equal or higher level than the target.  However, they will not give this move a chance to break through detect or protect.", "short_effect": "Causes a one-hit KO."}, {"name": "toxic", "power": null, "damage_class": "status", "effects": [{"pattern": "status_move_with_damage", "match": "Status move that deals damage", "fullMatch": ["status move with damage"]}, {"pattern": "zero_power_damage_move", "match": "Zero power move with damage effect", "fullMatch": ["zero power damage"]}], "effect_text": "Badly poisons the target.  Never misses when used by a poison-type Pokémon.", "short_effect": "Badly poisons the target, inflicting more damage every turn."}, {"name": "seismic-toss", "power": null, "damage_class": "physical", "effects": [{"pattern": "damage.*equal.*to.*user'?s.*level", "match": "damage equal to the user's level.  type immunity applies, but other type effects are ignored. inflicts damage equal to the user's level", "fullMatch": ["damage equal to the user's level.  type immunity applies, but other type effects are ignored. inflicts damage equal to the user's level"]}, {"pattern": "inflicts.*damage.*equal.*to.*the.*user'?s.*level", "match": "inflicts damage equal to the user's level.  type immunity applies, but other type effects are ignored. inflicts damage equal to the user's level", "fullMatch": ["inflicts damage equal to the user's level.  type immunity applies, but other type effects are ignored. inflicts damage equal to the user's level"]}, {"pattern": "zero_power_damage_move", "match": "Zero power move with damage effect", "fullMatch": ["zero power damage"]}], "effect_text": "Inflicts damage equal to the user's level.  Type immunity applies, but other type effects are ignored.", "short_effect": "Inflicts damage equal to the user's level."}, {"name": "night-shade", "power": null, "damage_class": "special", "effects": [{"pattern": "damage.*equal.*to.*user'?s.*level", "match": "damage equal to the user's level.  type immunity applies, but other type effects are ignored. inflicts damage equal to the user's level", "fullMatch": ["damage equal to the user's level.  type immunity applies, but other type effects are ignored. inflicts damage equal to the user's level"]}, {"pattern": "inflicts.*damage.*equal.*to.*the.*user'?s.*level", "match": "inflicts damage equal to the user's level.  type immunity applies, but other type effects are ignored. inflicts damage equal to the user's level", "fullMatch": ["inflicts damage equal to the user's level.  type immunity applies, but other type effects are ignored. inflicts damage equal to the user's level"]}, {"pattern": "zero_power_damage_move", "match": "Zero power move with damage effect", "fullMatch": ["zero power damage"]}], "effect_text": "Inflicts damage equal to the user's level.  Type immunity applies, but other type effects are ignored.", "short_effect": "Inflicts damage equal to the user's level."}, {"name": "dragon-rage", "power": null, "damage_class": "special", "effects": [{"pattern": "inflicts.*(\\d+).*damage", "match": "inflicts exactly 40 damage. inflicts 40 points of damage", "fullMatch": ["inflicts exactly 40 damage. inflicts 40 points of damage", "0"]}, {"pattern": "zero_power_damage_move", "match": "Zero power move with damage effect", "fullMatch": ["zero power damage"]}], "effect_text": "Inflicts exactly 40 damage.", "short_effect": "Inflicts 40 points of damage."}, {"name": "sonic-boom", "power": null, "damage_class": "special", "effects": [{"pattern": "inflicts.*(\\d+).*damage", "match": "inflicts exactly 20 damage. inflicts 20 points of damage", "fullMatch": ["inflicts exactly 20 damage. inflicts 20 points of damage", "0"]}, {"pattern": "zero_power_damage_move", "match": "Zero power move with damage effect", "fullMatch": ["zero power damage"]}], "effect_text": "Inflicts exactly 20 damage.", "short_effect": "Inflicts 20 points of damage."}, {"name": "counter", "power": null, "damage_class": "physical", "effects": [{"pattern": "inflicts.*twice.*the.*damage", "match": "inflicts twice the damage", "fullMatch": ["inflicts twice the damage"]}, {"pattern": "mirror.*move", "match": "mirror move", "fullMatch": ["mirror move"]}, {"pattern": "zero_power_damage_move", "match": "Zero power move with damage effect", "fullMatch": ["zero power damage"]}], "effect_text": "Targets the last opposing Pokémon to hit the user with a physical move this turn.  Inflicts twice the damage that move did to the user.  If there is no eligible target, this move will fail.  Type immunity applies, but other type effects are ignored.\n\nThis move cannot be copied by mirror move, nor selected by assist or metronome.", "short_effect": "Inflicts twice the damage the user received from the last physical hit it took."}, {"name": "mirror-move", "power": null, "damage_class": "status", "effects": [{"pattern": "mirror.*move", "match": "mirror coat, mirror move", "fullMatch": ["mirror coat, mirror move"]}], "effect_text": "Uses the last move targeted at the user by a Pokémon still on the field.  A move counts as targeting the user even if it hit multiple Pokémon, as long as the user was one of them; however, moves targeting the field itself do not count.  If the user has not been targeted by an appropriate move since entering the field, or if no Pokémon that targeted the user remains on the field, this move will fail.\n\nMoves that failed, missed, had no effect, or were blocked are still copied.\n\nAssist moves, time-delayed moves, “meta” moves that operate on other moves/Pokémon/abilities, and some other special moves cannot be copied and are ignored; if the last move to hit the user was such a move, the previous move will be used instead.  The full list of ignored moves is: acid armor, acupressure, after you, agility, ally switch, amnesia, aqua ring, aromatherapy, aromatic mist, assist, autotomize, barrier, baton pass, belch, belly drum, bide, bulk up, calm mind, camouflage, celebrate, charge, coil, conversion, conversion 2, copycat, cosmic power, cotton guard, counter, crafty shield, curse, defend order, defense curl, destiny bond, detect, doom desire, double team, dragon dance, electric terrain, endure, final gambit, flower shield, focus energy, focus punch, follow me, future sight, geomancy, grassy terrain, gravity, growth, grudge, guard split, hail, happy hour, harden, haze, heal bell, heal order, heal pulse, healing wish, helping hand, hold hands, hone claws, howl, imprison, ingrain, ion deluge, iron defense, kings shield, light screen, lucky chant, lunar dance, magic coat, magnet rise, magnetic flux, mat block, me first, meditate, metronome, milk drink, mimic, minimize, mirror coat, mirror move, mist, misty terrain, moonlight, morning sun, mud sport, nasty plot, nature power, perish song, power split, power trick, protect, psych up, quick guard, quiver dance, rage powder, rain dance, recover, recycle, reflect, reflect type, refresh, rest, rock polish, role play, roost, rototiller, safeguard, sandstorm, shadow blast, shadow bolt, shadow half, shadow rush, shadow shed, shadow sky, shadow storm, shadow wave, sharpen, shell smash, shift gear, sketch, slack off, sleep talk, snatch, soft boiled, spikes, spiky shield, spit up, splash, stealth rock, sticky web, stockpile, struggle, substitute, sunny day, swallow, swords dance, synthesis, tail glow, tailwind, teleport, toxic spikes, transform, water sport, wide guard, wish, withdraw and work up.\n\nThis move cannot be selected by assist, metronome, or sleep talk, nor forced by encore.", "short_effect": "Uses the target's last used move."}, {"name": "bide", "power": null, "damage_class": "physical", "effects": [{"pattern": "inflicts.*twice.*the.*damage", "match": "inflicts twice the damage it accumulated on the last pokémon to hit it.  damage", "fullMatch": ["inflicts twice the damage it accumulated on the last pokémon to hit it.  damage"]}, {"pattern": "zero_power_damage_move", "match": "Zero power move with damage effect", "fullMatch": ["zero power damage"]}], "effect_text": "User waits for two turns.  On the second turn, the user inflicts twice the damage it accumulated on the last Pokémon to hit it.  Damage inflicted is typeless.\n\nThis move cannot be selected by sleep talk.", "short_effect": "User waits for two turns, then hits back for twice the damage it took."}, {"name": "super-fang", "power": null, "damage_class": "physical", "effects": [{"pattern": "zero_power_damage_move", "match": "Zero power move with damage effect", "fullMatch": ["zero power damage"]}], "effect_text": "Inflicts typeless damage equal to half the target's remaining HP.", "short_effect": "Inflicts damage equal to half the target's HP."}, {"name": "psywave", "power": null, "damage_class": "special", "effects": [{"pattern": "inflicts.*(\\d+).*damage", "match": "inflicts typeless damage between 50% and 150% of the user's level, selected at random in increments of 10%. inflicts damage", "fullMatch": ["inflicts typeless damage between 50% and 150% of the user's level, selected at random in increments of 10%. inflicts damage", "0"]}, {"pattern": "random.*damage", "match": "random in increments of 10%. inflicts damage", "fullMatch": ["random in increments of 10%. inflicts damage"]}, {"pattern": "zero_power_damage_move", "match": "Zero power move with damage effect", "fullMatch": ["zero power damage"]}], "effect_text": "Inflicts typeless damage between 50% and 150% of the user's level, selected at random in increments of 10%.", "short_effect": "Inflicts damage between 50% and 150% of the user's level."}, {"name": "guillotine", "power": null, "damage_class": "physical", "effects": [{"pattern": "inflicts.*damage.*equal.*to.*(\\d+)", "match": "inflicts damage equal to the target's max hp.  ignores accuracy and evasion modifiers.  this move's accuracy is 30% plus 1", "fullMatch": ["inflicts damage equal to the target's max hp.  ignores accuracy and evasion modifiers.  this move's accuracy is 30% plus 1", "1"]}, {"pattern": "one.*hit.*ko", "match": "one-hit ko", "fullMatch": ["one-hit ko"]}, {"pattern": "ignores.*accuracy.*and.*evasion", "match": "ignores accuracy and evasion", "fullMatch": ["ignores accuracy and evasion"]}, {"pattern": "zero_power_damage_move", "match": "Zero power move with damage effect", "fullMatch": ["zero power damage"]}], "effect_text": "Inflicts damage equal to the target's max HP.  Ignores accuracy and evasion modifiers.  This move's accuracy is 30% plus 1% for each level the user is higher than the target.  If the user is a lower level than the target, this move will fail.\n\nBecause this move inflicts a specific and finite amount of damage, endure still prevents the target from fainting.\n\nThe effects of lock on, mind reader, and no guard still apply, as long as the user is equal or higher level than the target.  However, they will not give this move a chance to break through detect or protect.", "short_effect": "Causes a one-hit KO."}, {"name": "horn-drill", "power": null, "damage_class": "physical", "effects": [{"pattern": "inflicts.*damage.*equal.*to.*(\\d+)", "match": "inflicts damage equal to the target's max hp.  ignores accuracy and evasion modifiers.  this move's accuracy is 30% plus 1", "fullMatch": ["inflicts damage equal to the target's max hp.  ignores accuracy and evasion modifiers.  this move's accuracy is 30% plus 1", "1"]}, {"pattern": "one.*hit.*ko", "match": "one-hit ko", "fullMatch": ["one-hit ko"]}, {"pattern": "ignores.*accuracy.*and.*evasion", "match": "ignores accuracy and evasion", "fullMatch": ["ignores accuracy and evasion"]}, {"pattern": "zero_power_damage_move", "match": "Zero power move with damage effect", "fullMatch": ["zero power damage"]}], "effect_text": "Inflicts damage equal to the target's max HP.  Ignores accuracy and evasion modifiers.  This move's accuracy is 30% plus 1% for each level the user is higher than the target.  If the user is a lower level than the target, this move will fail.\n\nBecause this move inflicts a specific and finite amount of damage, endure still prevents the target from fainting.\n\nThe effects of lock on, mind reader, and no guard still apply, as long as the user is equal or higher level than the target.  However, they will not give this move a chance to break through detect or protect.", "short_effect": "Causes a one-hit KO."}, {"name": "fissure", "power": null, "damage_class": "physical", "effects": [{"pattern": "inflicts.*damage.*equal.*to.*(\\d+)", "match": "inflicts damage equal to the target's max hp.  ignores accuracy and evasion modifiers.  this move's accuracy is 30% plus 1", "fullMatch": ["inflicts damage equal to the target's max hp.  ignores accuracy and evasion modifiers.  this move's accuracy is 30% plus 1", "1"]}, {"pattern": "one.*hit.*ko", "match": "one-hit ko", "fullMatch": ["one-hit ko"]}, {"pattern": "ignores.*accuracy.*and.*evasion", "match": "ignores accuracy and evasion", "fullMatch": ["ignores accuracy and evasion"]}, {"pattern": "zero_power_damage_move", "match": "Zero power move with damage effect", "fullMatch": ["zero power damage"]}], "effect_text": "Inflicts damage equal to the target's max HP.  Ignores accuracy and evasion modifiers.  This move's accuracy is 30% plus 1% for each level the user is higher than the target.  If the user is a lower level than the target, this move will fail.\n\nBecause this move inflicts a specific and finite amount of damage, endure still prevents the target from fainting.\n\nThe effects of lock on, mind reader, and no guard still apply, as long as the user is equal or higher level than the target.  However, they will not give this move a chance to break through detect or protect.", "short_effect": "Causes a one-hit KO."}, {"name": "electro-ball", "power": null, "damage_class": "special", "effects": [{"pattern": "zero_power_damage_move", "match": "Zero power move with damage effect", "fullMatch": ["zero power damage"]}], "effect_text": "Inflicts regular damage.  The greater the user's Speed compared to the target's, the higher power this move has, to a maximum of 150.\n\nUser's Speed                     | Power\n-------------------------------- | ----:\nUp to 2× the target's Speed      |    60\nUp to 3× the target's Speed      |    80\nUp to 4× the target's Speed      |   120\nMore than 4× the target's Speed  |   150\n", "short_effect": "Power is higher when the user has greater Speed than the target, up to a maximum of 150."}, {"name": "gyro-ball", "power": null, "damage_class": "physical", "effects": [{"pattern": "zero_power_damage_move", "match": "Zero power move with damage effect", "fullMatch": ["zero power damage"]}], "effect_text": "Inflicts regular damage.  Power increases with the target's current Speed compared to the user, given by `1 + 25 * target Speed / user Speed`, capped at 150.", "short_effect": "Power raises when the user has lower Speed, up to a maximum of 150."}, {"name": "trump-card", "power": null, "damage_class": "special", "effects": [{"pattern": "ignores.*accuracy.*and.*evasion", "match": "ignores accuracy and evasion", "fullMatch": ["ignores accuracy and evasion"]}, {"pattern": "zero_power_damage_move", "match": "Zero power move with damage effect", "fullMatch": ["zero power damage"]}], "effect_text": "Inflicts regular damage.  Power is determined by the PP remaining for this move, after its PP cost is deducted.  Ignores accuracy and evasion modifiers.\n\nPP remaining | Power\n------------ | ----:\n4 or more    |    40\n3            |    50\n2            |    60\n1            |    80\n0            |   200\n\nIf this move is activated by another move, the activating move's PP is used to calculate power.", "short_effect": "Power increases when this move has less PP, up to a maximum of 200."}, {"name": "flail", "power": null, "damage_class": "physical", "effects": [{"pattern": "damage.*varies", "match": "damage.  power varies", "fullMatch": ["damage.  power varies"]}, {"pattern": "zero_power_damage_move", "match": "Zero power move with damage effect", "fullMatch": ["zero power damage"]}], "effect_text": "Inflicts regular damage.  Power varies inversely with the user's proportional remaining HP.\n\n64 * current HP / max HP | Power\n-----------------------: | ----:\n 0– 1                    |  200\n 2– 5                    |  150\n 6–12                    |  100\n13–21                    |   80\n22–42                    |   40\n43–64                    |   20\n", "short_effect": "Inflicts more damage when the user has less HP remaining, with a maximum of 200 power."}, {"name": "reversal", "power": null, "damage_class": "physical", "effects": [{"pattern": "damage.*varies", "match": "damage.  power varies", "fullMatch": ["damage.  power varies"]}, {"pattern": "zero_power_damage_move", "match": "Zero power move with damage effect", "fullMatch": ["zero power damage"]}], "effect_text": "Inflicts regular damage.  Power varies inversely with the user's proportional remaining HP.\n\n64 * current HP / max HP | Power\n-----------------------: | ----:\n 0– 1                    |  200\n 2– 5                    |  150\n 6–12                    |  100\n13–21                    |   80\n22–42                    |   40\n43–64                    |   20\n", "short_effect": "Inflicts more damage when the user has less HP remaining, with a maximum of 200 power."}], "damageFormulas": {"night-shade": {"type": "level_based", "multiplier": 1}, "bide": {"type": "bide", "turns": 2, "multiplier": 2}, "mirror-move": {"type": "mirror_move"}, "guillotine": {"type": "ohko", "accuracy_formula": "level_difference"}, "flail": {"type": "low_hp_based", "power_table": [20, 40, 80, 100, 150, 200]}, "horn-drill": {"type": "ohko", "accuracy_formula": "level_difference"}, "sonic-boom": {"type": "fixed", "value": 20}, "counter": {"type": "counter", "multiplier": 2, "damage_type": "physical"}, "seismic-toss": {"type": "level_based", "multiplier": 1}, "dragon-rage": {"type": "fixed", "value": 40}, "fissure": {"type": "ohko", "accuracy_formula": "level_difference"}, "super-fang": {"type": "target_hp_percentage", "percentage": 0.5}, "psywave": {"type": "random_level_based", "min_multiplier": 0.5, "max_multiplier": 1.5}, "electro-ball": {"type": "speed_based", "base_power_table": [60, 80, 120, 150]}, "gyro-ball": {"type": "inverse_speed_based", "max_power": 150}, "reversal": {"type": "low_hp_based", "power_table": [20, 40, 80, 100, 150, 200]}}, "totalSpecialMoves": 43}