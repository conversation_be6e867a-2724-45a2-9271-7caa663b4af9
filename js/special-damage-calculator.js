/**
 * Special Damage Calculator
 * Handles Pokemon moves with special damage formulas
 */

class SpecialDamageCalculator {
  constructor() {
    this.specialMoves = null;
    this.damageFormulas = null;
    this.loadSpecialMoveData();
  }

  async loadSpecialMoveData() {
    try {
      const response = await fetch('./data/metadata/special-moves.json');
      if (response.ok) {
        const data = await response.json();
        this.damageFormulas = data.damageFormulas;
        console.log('✅ Special move formulas loaded:', Object.keys(this.damageFormulas).length, 'moves');
      } else {
        console.warn('⚠️ Could not load special move data, using fallback calculations');
        this.damageFormulas = {};
      }
    } catch (error) {
      console.warn('⚠️ Error loading special move data:', error.message);
      this.damageFormulas = {};
    }
  }

  /**
   * Check if a move has a special damage formula
   */
  hasSpecialFormula(moveName) {
    return this.damageFormulas && this.damageFormulas[moveName];
  }

  /**
   * Calculate damage for moves with special formulas
   */
  calculateSpecialDamage(attacker, defender, move, battleContext = {}) {
    if (!this.hasSpecialFormula(move.name)) {
      return null; // Use normal damage calculation
    }

    const formula = this.damageFormulas[move.name];
    console.log(`🔮 Using special formula for ${move.name}:`, formula.type);

    switch (formula.type) {
      case 'fixed':
        return this.calculateFixedDamage(formula, attacker, defender, move);
      
      case 'level_based':
        return this.calculateLevelBasedDamage(formula, attacker, defender, move);
      
      case 'target_hp_percentage':
        return this.calculateHPPercentageDamage(formula, attacker, defender, move);
      
      case 'counter':
        return this.calculateCounterDamage(formula, attacker, defender, move, battleContext);
      
      case 'random_level_based':
        return this.calculateRandomLevelDamage(formula, attacker, defender, move);
      
      case 'ohko':
        return this.calculateOHKODamage(formula, attacker, defender, move);
      
      case 'low_hp_based':
        return this.calculateLowHPBasedDamage(formula, attacker, defender, move);
      
      case 'speed_based':
        return this.calculateSpeedBasedDamage(formula, attacker, defender, move);
      
      case 'inverse_speed_based':
        return this.calculateInverseSpeedBasedDamage(formula, attacker, defender, move);
      
      case 'bide':
        return this.calculateBideDamage(formula, attacker, defender, move, battleContext);
      
      case 'mirror_move':
        return this.calculateMirrorMoveDamage(formula, attacker, defender, move, battleContext);
      
      default:
        console.warn(`Unknown special formula type: ${formula.type}`);
        return null;
    }
  }

  /**
   * Fixed damage moves (Dragon Rage, Sonic Boom)
   */
  calculateFixedDamage(formula, attacker, defender, move) {
    const damage = formula.value;
    
    // Apply type immunity
    const effectiveness = getTypeEffectiveness(move.type, defender.types);
    if (effectiveness === 0) {
      return { damage: 0, message: `${move.name} had no effect!` };
    }
    
    return { 
      damage: damage, 
      message: `${move.name} deals fixed ${damage} damage!`,
      isSpecial: true
    };
  }

  /**
   * Level-based damage (Seismic Toss, Night Shade)
   */
  calculateLevelBasedDamage(formula, attacker, defender, move) {
    const damage = attacker.level * formula.multiplier;
    
    // Apply type immunity
    const effectiveness = getTypeEffectiveness(move.type, defender.types);
    if (effectiveness === 0) {
      return { damage: 0, message: `${move.name} had no effect!` };
    }
    
    return { 
      damage: damage, 
      message: `${move.name} deals damage equal to user's level (${damage})!`,
      isSpecial: true
    };
  }

  /**
   * HP percentage damage (Super Fang)
   */
  calculateHPPercentageDamage(formula, attacker, defender, move) {
    const damage = Math.floor(defender.currentHP * formula.percentage);
    
    // Apply type immunity
    const effectiveness = getTypeEffectiveness(move.type, defender.types);
    if (effectiveness === 0) {
      return { damage: 0, message: `${move.name} had no effect!` };
    }
    
    return { 
      damage: damage, 
      message: `${move.name} cuts the target's HP in half!`,
      isSpecial: true
    };
  }

  /**
   * Counter damage (Counter)
   */
  calculateCounterDamage(formula, attacker, defender, move, battleContext) {
    const lastDamage = battleContext.lastPhysicalDamage || 0;
    const damage = lastDamage * formula.multiplier;
    
    if (lastDamage === 0) {
      return { damage: 0, message: `${move.name} failed!` };
    }
    
    return { 
      damage: damage, 
      message: `${move.name} counters with double damage!`,
      isSpecial: true
    };
  }

  /**
   * Random level-based damage (Psywave)
   */
  calculateRandomLevelDamage(formula, attacker, defender, move) {
    const minDamage = Math.floor(attacker.level * formula.min_multiplier);
    const maxDamage = Math.floor(attacker.level * formula.max_multiplier);
    const damage = Math.floor(Math.random() * (maxDamage - minDamage + 1)) + minDamage;
    
    // Apply type immunity
    const effectiveness = getTypeEffectiveness(move.type, defender.types);
    if (effectiveness === 0) {
      return { damage: 0, message: `${move.name} had no effect!` };
    }
    
    return { 
      damage: damage, 
      message: `${move.name} deals random damage (${damage})!`,
      isSpecial: true
    };
  }

  /**
   * One-Hit KO moves (Guillotine, Horn Drill, Fissure)
   */
  calculateOHKODamage(formula, attacker, defender, move) {
    // OHKO moves fail if target is higher level
    if (defender.level > attacker.level) {
      return { damage: 0, message: `${move.name} failed!` };
    }
    
    // Calculate accuracy based on level difference
    const levelDiff = attacker.level - defender.level;
    const accuracy = Math.min(30 + levelDiff, 100);
    
    // Check if move hits (simplified - in real game this would be handled by accuracy system)
    const hitRoll = Math.random() * 100;
    if (hitRoll > accuracy) {
      return { damage: 0, message: `${move.name} missed!` };
    }
    
    // Apply type immunity
    const effectiveness = getTypeEffectiveness(move.type, defender.types);
    if (effectiveness === 0) {
      return { damage: 0, message: `${move.name} had no effect!` };
    }
    
    return { 
      damage: defender.currentHP, 
      message: `${move.name} is a one-hit KO!`,
      isSpecial: true,
      isOHKO: true
    };
  }

  /**
   * Low HP based damage (Flail, Reversal)
   */
  calculateLowHPBasedDamage(formula, attacker, defender, move) {
    const hpPercentage = attacker.currentHP / attacker.maxHP;
    let power;
    
    if (hpPercentage > 0.6875) power = formula.power_table[0]; // 20
    else if (hpPercentage > 0.3542) power = formula.power_table[1]; // 40
    else if (hpPercentage > 0.2083) power = formula.power_table[2]; // 80
    else if (hpPercentage > 0.1042) power = formula.power_table[3]; // 100
    else if (hpPercentage > 0.0417) power = formula.power_table[4]; // 150
    else power = formula.power_table[5]; // 200
    
    // Use normal damage calculation with the determined power
    const modifiedMove = { ...move, power: power };
    const damage = calculateDamage(attacker, defender, modifiedMove);
    
    return { 
      damage: damage, 
      message: `${move.name} gains power from low HP! (Power: ${power})`,
      isSpecial: true
    };
  }

  /**
   * Speed-based damage (Electro Ball)
   */
  calculateSpeedBasedDamage(formula, attacker, defender, move) {
    const speedRatio = attacker.stats.speed / defender.stats.speed;
    let power;
    
    if (speedRatio >= 4) power = formula.base_power_table[3]; // 150
    else if (speedRatio >= 3) power = formula.base_power_table[2]; // 120
    else if (speedRatio >= 2) power = formula.base_power_table[1]; // 80
    else power = formula.base_power_table[0]; // 60
    
    // Use normal damage calculation with the determined power
    const modifiedMove = { ...move, power: power };
    const damage = calculateDamage(attacker, defender, modifiedMove);
    
    return { 
      damage: damage, 
      message: `${move.name} power based on speed! (Power: ${power})`,
      isSpecial: true
    };
  }

  /**
   * Inverse speed-based damage (Gyro Ball)
   */
  calculateInverseSpeedBasedDamage(formula, attacker, defender, move) {
    const power = Math.min(
      Math.floor((25 * defender.stats.speed) / attacker.stats.speed) + 1,
      formula.max_power
    );
    
    // Use normal damage calculation with the determined power
    const modifiedMove = { ...move, power: power };
    const damage = calculateDamage(attacker, defender, modifiedMove);
    
    return { 
      damage: damage, 
      message: `${move.name} power based on speed difference! (Power: ${power})`,
      isSpecial: true
    };
  }

  /**
   * Bide damage (accumulates damage over turns)
   */
  calculateBideDamage(formula, attacker, defender, move, battleContext) {
    // This would need to be implemented with turn-based state management
    // For now, return a placeholder
    const accumulatedDamage = battleContext.bideDamage || 0;
    const damage = accumulatedDamage * formula.multiplier;
    
    return { 
      damage: damage, 
      message: `${move.name} unleashes accumulated damage!`,
      isSpecial: true
    };
  }

  /**
   * Mirror Move (copies last used move)
   */
  calculateMirrorMoveDamage(formula, attacker, defender, move, battleContext) {
    // This would need access to the last used move
    // For now, return a placeholder
    return { 
      damage: 0, 
      message: `${move.name} copies the last move used!`,
      isSpecial: true,
      requiresSpecialHandling: true
    };
  }
}

// Create global instance
const specialDamageCalculator = new SpecialDamageCalculator();

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
  module.exports = SpecialDamageCalculator;
}
