#!/usr/bin/env node

/**
 * Special Moves Analyzer and Downloader
 * Identifies and downloads Pokemon moves with special damage formulas
 */

const fs = require('fs').promises;
const path = require('path');

class SpecialMovesAnalyzer {
  constructor() {
    this.baseUrl = 'https://pokeapi.co/api/v2';
    this.dataDir = path.join(__dirname, '..', 'data');
    this.requestDelay = 100;
    this.specialMoves = new Map();
    this.specialMovePatterns = [
      // Fixed damage patterns
      /inflicts.*damage.*equal.*to.*(\d+)/i,
      /inflicts.*(\d+).*damage/i,
      /deals.*(\d+).*damage/i,
      /always.*inflicts.*(\d+)/i,
      
      // Level-based damage
      /damage.*equal.*to.*user'?s.*level/i,
      /inflicts.*damage.*equal.*to.*the.*user'?s.*level/i,
      
      // HP-based damage
      /damage.*equal.*to.*user'?s.*current.*hp/i,
      /damage.*equal.*to.*user'?s.*remaining.*hp/i,
      /damage.*equal.*to.*half.*user'?s.*current.*hp/i,
      
      // Counter/Mirror moves
      /returns.*damage.*taken/i,
      /inflicts.*twice.*the.*damage/i,
      /counter.*attack/i,
      /mirror.*move/i,
      
      // Variable damage
      /damage.*varies/i,
      /random.*damage/i,
      /variable.*damage/i,
      
      // Special mechanics
      /one.*hit.*ko/i,
      /ohko/i,
      /always.*results.*in.*critical.*hit/i,
      /ignores.*accuracy.*and.*evasion/i,
      
      // Status moves that deal damage
      /status.*move.*that.*deals.*damage/i,
      
      // Speed-based damage
      /damage.*based.*on.*speed/i,
      /faster.*the.*user/i,
      
      // Weight-based damage
      /damage.*based.*on.*weight/i,
      /heavier.*the.*target/i
    ];
  }

  async delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  async fetchWithRetry(url, maxRetries = 3) {
    for (let i = 0; i < maxRetries; i++) {
      try {
        console.log(`Fetching: ${url}`);
        const response = await fetch(url);
        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        return await response.json();
      } catch (error) {
        console.error(`Attempt ${i + 1} failed for ${url}:`, error.message);
        if (i === maxRetries - 1) throw error;
        await this.delay(1000 * (i + 1));
      }
    }
  }

  async loadExistingMoves() {
    console.log('Loading existing move data...');
    const movesDir = path.join(this.dataDir, 'moves');
    const files = await fs.readdir(movesDir);
    
    const moves = [];
    for (const file of files) {
      if (file.endsWith('.json')) {
        try {
          const filePath = path.join(movesDir, file);
          const moveData = JSON.parse(await fs.readFile(filePath, 'utf8'));
          moves.push(moveData);
        } catch (error) {
          console.error(`Error loading ${file}:`, error.message);
        }
      }
    }
    
    console.log(`Loaded ${moves.length} existing moves`);
    return moves;
  }

  analyzeMove(move) {
    const specialEffects = [];
    
    if (!move.effect_entries || move.effect_entries.length === 0) {
      return null;
    }

    const effectText = move.effect_entries[0].effect || '';
    const shortEffect = move.effect_entries[0].short_effect || '';
    const combinedText = `${effectText} ${shortEffect}`.toLowerCase();

    // Check for special damage patterns
    for (const pattern of this.specialMovePatterns) {
      const match = combinedText.match(pattern);
      if (match) {
        specialEffects.push({
          pattern: pattern.source,
          match: match[0],
          fullMatch: match
        });
      }
    }

    // Check for status moves that deal damage
    if (move.damage_class === 'status' && (move.power > 0 || combinedText.includes('damage'))) {
      specialEffects.push({
        pattern: 'status_move_with_damage',
        match: 'Status move that deals damage',
        fullMatch: ['status move with damage']
      });
    }

    // Check for moves with null/0 power but damage effects
    if ((move.power === null || move.power === 0) && combinedText.includes('damage')) {
      specialEffects.push({
        pattern: 'zero_power_damage_move',
        match: 'Zero power move with damage effect',
        fullMatch: ['zero power damage']
      });
    }

    return specialEffects.length > 0 ? {
      name: move.name,
      power: move.power,
      damage_class: move.damage_class,
      effects: specialEffects,
      effect_text: effectText,
      short_effect: shortEffect
    } : null;
  }

  async findSpecialMovesInGen1() {
    console.log('Analyzing existing moves for special damage formulas...\n');
    
    const existingMoves = await this.loadExistingMoves();
    const specialMoves = [];
    
    for (const move of existingMoves) {
      const analysis = this.analyzeMove(move);
      if (analysis) {
        specialMoves.push(analysis);
        console.log(`🔍 Found special move: ${analysis.name}`);
        console.log(`   Power: ${analysis.power}, Class: ${analysis.damage_class}`);
        console.log(`   Effect: ${analysis.short_effect}`);
        console.log('');
      }
    }
    
    return specialMoves;
  }

  async downloadAdditionalSpecialMoves() {
    console.log('Checking for additional special moves not yet downloaded...\n');
    
    // Known special moves from Gen 1 that might not be in our data
    const knownSpecialMoves = [
      'seismic-toss', 'night-shade', 'dragon-rage', 'sonic-boom',
      'counter', 'mirror-move', 'bide', 'super-fang', 'psywave',
      'guillotine', 'horn-drill', 'fissure', 'electro-ball',
      'gyro-ball', 'trump-card', 'flail', 'reversal'
    ];
    
    const newMoves = [];
    
    for (const moveName of knownSpecialMoves) {
      try {
        // Check if we already have this move
        const fileName = `${moveName}.json`;
        const filePath = path.join(this.dataDir, 'moves', fileName);
        
        try {
          await fs.access(filePath);
          console.log(`✓ Already have: ${moveName}`);
          continue;
        } catch {
          // File doesn't exist, download it
        }
        
        const moveData = await this.fetchWithRetry(`${this.baseUrl}/move/${moveName}`);
        
        // Process and save the move data
        const processedMove = {
          name: moveData.name,
          power: moveData.power,
          accuracy: moveData.accuracy,
          pp: moveData.pp,
          type: moveData.type.name,
          damage_class: moveData.damage_class.name,
          effect_entries: moveData.effect_entries.filter(entry => entry.language.name === 'en')
        };
        
        await fs.writeFile(filePath, JSON.stringify(processedMove, null, 2));
        
        // Analyze the new move
        const analysis = this.analyzeMove(processedMove);
        if (analysis) {
          newMoves.push(analysis);
          console.log(`✓ Downloaded special move: ${moveName}`);
        } else {
          console.log(`✓ Downloaded move: ${moveName} (not special)`);
        }
        
        await this.delay(this.requestDelay);
        
      } catch (error) {
        console.error(`✗ Failed to download ${moveName}:`, error.message);
      }
    }
    
    return newMoves;
  }

  generateSpecialMoveFormulas(specialMoves) {
    console.log('\n🧮 Generating special damage formulas...\n');
    
    const formulas = {};
    
    for (const move of specialMoves) {
      const moveName = move.name;
      let formula = null;
      
      // Analyze effects to determine formula
      for (const effect of move.effects) {
        const text = effect.match.toLowerCase();
        
        // Fixed damage moves
        if (text.includes('damage equal to 40') || moveName === 'dragon-rage') {
          formula = { type: 'fixed', value: 40 };
        } else if (text.includes('damage equal to 20') || moveName === 'sonic-boom') {
          formula = { type: 'fixed', value: 20 };
        }
        
        // Level-based damage
        else if (text.includes('damage equal to the user\'s level') || 
                 moveName === 'seismic-toss' || moveName === 'night-shade') {
          formula = { type: 'level_based', multiplier: 1 };
        }
        
        // HP-based damage
        else if (text.includes('half') && text.includes('current hp') || moveName === 'super-fang') {
          formula = { type: 'target_hp_percentage', percentage: 0.5 };
        }
        
        // Counter moves
        else if (text.includes('returns') && text.includes('damage') || moveName === 'counter') {
          formula = { type: 'counter', multiplier: 2, damage_type: 'physical' };
        } else if (moveName === 'mirror-move') {
          formula = { type: 'mirror_move' };
        }
        
        // Variable damage moves
        else if (moveName === 'psywave') {
          formula = { type: 'random_level_based', min_multiplier: 0.5, max_multiplier: 1.5 };
        }
        
        // OHKO moves
        else if (text.includes('one hit ko') || text.includes('ohko') ||
                 ['guillotine', 'horn-drill', 'fissure'].includes(moveName)) {
          formula = { type: 'ohko', accuracy_formula: 'level_difference' };
        }
        
        // Bide (special accumulation move)
        else if (moveName === 'bide') {
          formula = { type: 'bide', turns: 2, multiplier: 2 };
        }
        
        // Speed-based moves (for future generations)
        else if (moveName === 'electro-ball') {
          formula = { type: 'speed_based', base_power_table: [60, 80, 120, 150] };
        } else if (moveName === 'gyro-ball') {
          formula = { type: 'inverse_speed_based', max_power: 150 };
        }
        
        // HP-based moves
        else if (['flail', 'reversal'].includes(moveName)) {
          formula = { type: 'low_hp_based', power_table: [20, 40, 80, 100, 150, 200] };
        }
      }
      
      if (formula) {
        formulas[moveName] = formula;
        console.log(`📐 ${moveName}: ${JSON.stringify(formula)}`);
      }
    }
    
    return formulas;
  }

  async saveSpecialMoveData(specialMoves, formulas) {
    const specialMoveData = {
      version: '1.0.0',
      generatedAt: new Date().toISOString(),
      specialMoves: specialMoves,
      damageFormulas: formulas,
      totalSpecialMoves: specialMoves.length
    };
    
    const filePath = path.join(this.dataDir, 'metadata', 'special-moves.json');
    await fs.writeFile(filePath, JSON.stringify(specialMoveData, null, 2));
    
    console.log(`\n💾 Saved special move data to: ${filePath}`);
    console.log(`📊 Total special moves found: ${specialMoves.length}`);
  }

  async run() {
    console.log('🔍 Special Moves Analysis and Download\n');
    
    try {
      // Analyze existing moves
      const existingSpecialMoves = await this.findSpecialMovesInGen1();
      
      // Download additional known special moves
      const newSpecialMoves = await this.downloadAdditionalSpecialMoves();
      
      // Combine all special moves
      const allSpecialMoves = [...existingSpecialMoves, ...newSpecialMoves];
      
      // Generate formulas
      const formulas = this.generateSpecialMoveFormulas(allSpecialMoves);
      
      // Save data
      await this.saveSpecialMoveData(allSpecialMoves, formulas);
      
      console.log('\n✅ Special moves analysis completed!');
      
    } catch (error) {
      console.error('\n❌ Analysis failed:', error.message);
      throw error;
    }
  }
}

// Run the analyzer if this script is executed directly
if (require.main === module) {
  const analyzer = new SpecialMovesAnalyzer();
  
  analyzer.run()
    .then(() => {
      console.log('Special moves analysis completed successfully!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('Special moves analysis failed:', error);
      process.exit(1);
    });
}

module.exports = SpecialMovesAnalyzer;
