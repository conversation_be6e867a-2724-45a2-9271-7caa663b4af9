{"name": "pokemon-battle-game", "version": "1.0.0", "description": "A Pokemon battle game with offline data caching", "main": "index.html", "scripts": {"download-data": "node scripts/download-pokemon-data.js", "download-data-50": "node scripts/download-pokemon-data.js 50", "generate-indexes": "node scripts/generate-indexes.js", "analyze-special-moves": "node scripts/analyze-special-moves.js", "setup-offline": "npm run download-data && npm run analyze-special-moves && npm run generate-indexes", "setup-offline-quick": "npm run download-data-50 && npm run analyze-special-moves && npm run generate-indexes", "serve": "python3 -m http.server 8000", "serve-alt": "python -m SimpleHTTPServer 8000", "test-offline": "npm run serve", "test-special-moves": "echo 'Open http://localhost:8000/test-special-moves.html in your browser'"}, "keywords": ["pokemon", "game", "battle", "offline", "pokeapi"], "author": "Pokemon Game Developer", "license": "MIT", "engines": {"node": ">=14.0.0"}, "devDependencies": {}, "dependencies": {}, "repository": {"type": "git", "url": "."}, "bugs": {"url": "."}, "homepage": "."}