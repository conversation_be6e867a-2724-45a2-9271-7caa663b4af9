<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Pokemon Status Effects Test</title>
    <style>
        body {
            font-family: 'Courier New', monospace;
            background: #1a1a1a;
            color: #00ff00;
            padding: 20px;
            margin: 0;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
        }
        
        .header {
            text-align: center;
            border: 2px solid #00ff00;
            padding: 20px;
            margin-bottom: 20px;
            background: rgba(0, 255, 0, 0.1);
        }
        
        button {
            background: #333;
            color: #00ff00;
            border: 2px solid #00ff00;
            padding: 10px 20px;
            margin: 5px;
            cursor: pointer;
            font-family: inherit;
            font-size: 14px;
        }
        
        button:hover {
            background: rgba(0, 255, 0, 0.2);
        }
        
        .output {
            background: #000;
            border: 1px solid #333;
            padding: 15px;
            margin: 10px 0;
            height: 400px;
            overflow-y: auto;
            font-size: 12px;
            white-space: pre-wrap;
        }
        
        .pass { color: #00ff00; }
        .fail { color: #ff0000; }
        .info { color: #ffff00; }
        .warning { color: #ff8800; }
        
        .test-section {
            border: 1px solid #333;
            margin: 20px 0;
            padding: 15px;
            background: rgba(0, 0, 0, 0.3);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧪 Simple Pokemon Status Effects Test</h1>
            <p>Browser-compatible status effects testing</p>
        </div>

        <div class="test-section">
            <h3>🎮 Test Controls</h3>
            <button onclick="runSimpleTests()">Run Simple Tests</button>
            <button onclick="testStatusPersistence()">Test Status Persistence</button>
            <button onclick="testStatusClearing()">Test Status Clearing</button>
            <button onclick="clearOutput()">Clear Output</button>
        </div>

        <div class="test-section">
            <h3>📊 Test Output</h3>
            <div id="test-output" class="output"></div>
        </div>
    </div>

    <script>
        // Simple Mock Pokemon Class
        class SimplePokemon {
            constructor(name) {
                this.name = name;
                this.maxHP = 100;
                this.currentHP = 100;
                this.status = null;
                this.confusion = false;
                this.fainted = false;
            }

            setStatus(status) {
                if (this.status || this.fainted) return false;
                this.status = status;
                return true;
            }

            setConfusion() {
                if (this.confusion || this.fainted) return false;
                this.confusion = true;
                return true;
            }

            clearStatus() {
                this.status = null;
                this.confusion = false;
            }

            takeDamage(damage) {
                this.currentHP = Math.max(0, this.currentHP - damage);
                if (this.currentHP === 0) {
                    this.fainted = true;
                    this.clearStatus(); // Clear status on faint
                }
            }

            processStatusDamage() {
                if (this.status === "poison" || this.status === "burn") {
                    const damage = Math.floor(this.maxHP * 0.125); // 1/8 damage
                    this.takeDamage(damage);
                    return damage;
                }
                return 0;
            }

            getStatusText() {
                const statusTexts = [];
                if (this.status) statusTexts.push(this.status.toUpperCase());
                if (this.confusion) statusTexts.push("CONFUSED");
                return statusTexts.join(", ");
            }
        }

        // Logging functions
        let testOutput = document.getElementById('test-output');

        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const className = type;
            testOutput.innerHTML += `<span class="${className}">[${timestamp}] ${message}</span>\n`;
            testOutput.scrollTop = testOutput.scrollHeight;
        }

        function clearOutput() {
            testOutput.innerHTML = '';
        }

        // Test functions
        function runSimpleTests() {
            clearOutput();
            log('🧪 Starting Simple Status Effects Tests...', 'info');
            
            let passed = 0;
            let total = 0;

            // Test 1: Status Application
            total++;
            const pokemon1 = new SimplePokemon("Pikachu");
            const poisonResult = pokemon1.setStatus("poison");
            if (poisonResult && pokemon1.status === "poison") {
                log('✅ PASS - Status application works', 'pass');
                passed++;
            } else {
                log('❌ FAIL - Status application failed', 'fail');
            }

            // Test 2: Multiple Status Blocking
            total++;
            const burnResult = pokemon1.setStatus("burn");
            if (!burnResult && pokemon1.status === "poison") {
                log('✅ PASS - Multiple status blocked correctly', 'pass');
                passed++;
            } else {
                log('❌ FAIL - Multiple status not blocked', 'fail');
            }

            // Test 3: Confusion with existing status
            total++;
            const confusionResult = pokemon1.setConfusion();
            if (confusionResult && pokemon1.confusion && pokemon1.status === "poison") {
                log('✅ PASS - Confusion works with existing status', 'pass');
                passed++;
            } else {
                log('❌ FAIL - Confusion with status failed', 'fail');
            }

            // Test 4: Status damage
            total++;
            const hpBefore = pokemon1.currentHP;
            const damage = pokemon1.processStatusDamage();
            const expectedDamage = Math.floor(pokemon1.maxHP * 0.125);
            if (damage === expectedDamage && pokemon1.currentHP === hpBefore - damage) {
                log('✅ PASS - Status damage calculation correct', 'pass');
                passed++;
            } else {
                log('❌ FAIL - Status damage calculation wrong', 'fail');
            }

            log(`\n📊 Results: ${passed}/${total} tests passed (${((passed/total)*100).toFixed(1)}%)`, 
                passed === total ? 'pass' : 'warning');
        }

        function testStatusPersistence() {
            clearOutput();
            log('🔄 Testing Status Persistence During Pokemon Switching...', 'info');
            
            let passed = 0;
            let total = 0;

            // Create two Pokemon
            const pokemon1 = new SimplePokemon("Pikachu");
            const pokemon2 = new SimplePokemon("Charizard");

            // Apply status to Pokemon 1
            pokemon1.setStatus("poison");
            pokemon1.setConfusion();
            log(`Applied poison and confusion to ${pokemon1.name}`, 'info');

            // Test 1: New Pokemon should NOT inherit status
            total++;
            if (pokemon2.status === null && pokemon2.confusion === false) {
                log('✅ PASS - New Pokemon does NOT inherit status effects', 'pass');
                passed++;
            } else {
                log('❌ FAIL - New Pokemon inherited status effects!', 'fail');
            }

            // Test 2: Original Pokemon retains status when "switched out"
            total++;
            if (pokemon1.status === "poison" && pokemon1.confusion === true) {
                log('✅ PASS - Original Pokemon retains status when switched out', 'pass');
                passed++;
            } else {
                log('❌ FAIL - Original Pokemon lost status when switched out', 'fail');
            }

            // Test 3: Status returns when Pokemon switches back
            total++;
            // Simulate switching back (status should still be there)
            if (pokemon1.status === "poison" && pokemon1.confusion === true) {
                log('✅ PASS - Status returns when original Pokemon switches back', 'pass');
                passed++;
            } else {
                log('❌ FAIL - Status did not return with original Pokemon', 'fail');
            }

            log(`\n📊 Persistence Results: ${passed}/${total} tests passed (${((passed/total)*100).toFixed(1)}%)`, 
                passed === total ? 'pass' : 'warning');
        }

        function testStatusClearing() {
            clearOutput();
            log('💀 Testing Status Clearing on Pokemon Fainting...', 'info');
            
            let passed = 0;
            let total = 0;

            const pokemon = new SimplePokemon("Charizard");
            
            // Apply status effects
            pokemon.setStatus("burn");
            pokemon.setConfusion();
            log(`Applied burn and confusion to ${pokemon.name}`, 'info');

            // Simulate fainting
            pokemon.takeDamage(pokemon.maxHP);
            log(`${pokemon.name} took ${pokemon.maxHP} damage and fainted`, 'info');

            // Test 1: Status cleared on fainting
            total++;
            if (pokemon.status === null) {
                log('✅ PASS - Status cleared on fainting', 'pass');
                passed++;
            } else {
                log('❌ FAIL - Status not cleared on fainting', 'fail');
            }

            // Test 2: Confusion cleared on fainting
            total++;
            if (pokemon.confusion === false) {
                log('✅ PASS - Confusion cleared on fainting', 'pass');
                passed++;
            } else {
                log('❌ FAIL - Confusion not cleared on fainting', 'fail');
            }

            // Test 3: Pokemon is actually fainted
            total++;
            if (pokemon.fainted === true && pokemon.currentHP === 0) {
                log('✅ PASS - Pokemon is properly fainted', 'pass');
                passed++;
            } else {
                log('❌ FAIL - Pokemon fainting not working correctly', 'fail');
            }

            log(`\n📊 Clearing Results: ${passed}/${total} tests passed (${((passed/total)*100).toFixed(1)}%)`, 
                passed === total ? 'pass' : 'warning');
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', () => {
            log('🎮 Simple Status Effects Test Ready!', 'pass');
            log('Click the test buttons to run specific tests.', 'info');
        });
    </script>
</body>
</html>
