# Augment Development Notes

## Project Overview

Vollständiges Pokemon-Kampfspiel mit HTML, CSS und JavaScript. Browser-first Ansatz mit vollständiger Offline-Funktionalität durch lokale Datenhaltung. Spätere Konvertierung zu mobiler App mit Capacitor möglich.

## Aktuelle Projektstruktur (Stand: 2025-07-22)

### Kernkomponenten

- **Vollständiges Kampfsystem** mit allen offiziellen Pokemon-Mechaniken
- **Offline-Daten-System** mit lokaler JSON-Datenhaltung
- **Drei-stufiges KI-System** (Basic, Standard, Expert)
- **Responsive UI** mit Animationen und Touch-Support
- **Authentische Pokemon-Evolution** basierend auf Level und PokeAPI-Daten

### Technische Architektur

- **Frontend**: Vanilla JavaScript ES6+ mit modularer Klassenstruktur
- **Datenmanagement**: Hybrid-System (Offline JSON + PokeAPI Fallback)
- **UI-System**: CSS Grid/Flexbox mit Canvas-freier Implementierung
- **Caching**: localStorage + lokale JSON-Dateien
- **Build-System**: Node.js Scripts für Datenmanagement

### Datenquellen

- **Primär**: Lokale JSON-Dateien (data/ Verzeichnis)
- **Fallback**: PokeAPI (https://pokeapi.co) bei fehlenden Daten
- **Umfang**: Erste 151 Pokemon mit vollständigen Movesets und Evolution-Ketten

## Implementierte Features

### ✅ Vollständig implementiert

1. **Pokemon-System**

   - Authentische Level-basierte Evolution (Bulbasaur → Ivysaur → Venusaur)
   - Level-angemessene Movesets (keine unmöglichen Kombinationen)
   - Vollständige Stat-Berechnung mit Level-Skalierung
   - Status-Effekte (Poison, Burn, Paralysis, Sleep, Freeze)

2. **Kampfsystem**

   - Offizielle Pokemon-Schadensformel
   - 18x18 Typ-Effektivitäts-Matrix
   - STAB-Bonus (1.5x für gleiche Typen)
   - Kritische Treffer und Zufallsfaktoren
   - Pokemon-Wechsel und Team-Management

3. **KI-System**

   - Basic AI: Zufällige Züge
   - Standard AI: Typ-Effektivität berücksichtigen
   - Expert AI: Strategische Entscheidungen mit Schadens-Kalkulation

4. **Offline-Daten-System**

   - Vollständige lokale Datenhaltung
   - Automatischer Fallback zur PokeAPI
   - Index-Dateien für schnelle Lookups
   - Spezial-Attacken mit benutzerdefinierten Schadens-Formeln

5. **UI-System**
   - Responsive Design mit Mobile-First Ansatz
   - Animierte HP-Balken und Schadens-Anzeigen
   - Touch-optimierte Bedienung
   - Screen-Management-System

### 🔄 In Entwicklung

- Performance-Optimierungen
- Erweiterte Animationen
- Mobile App Konvertierung (Capacitor)

## Technische Implementierung

### Modulare Architektur

Das Projekt ist in klar getrennte Module aufgeteilt:

1. **Kern-Module** (`js/`)

   - `config.js`: Zentrale Konfiguration und Konstanten
   - `pokemon.js`: Pokemon-Klasse und Evolution-Logik
   - `battle-system.js`: Kampf-Mechaniken und Turn-Management
   - `ai.js`: KI-System mit drei Schwierigkeitsgraden
   - `ui.js`: UI-Management und Animationen
   - `api.js`: PokeAPI Integration mit Caching
   - `offline-data-loader.js`: Lokale Daten-Verwaltung
   - `special-damage-calculator.js`: Spezial-Attacken Berechnungen
   - `main.js`: Haupt-Controller und Game-Loop

2. **Daten-Management** (`data/`)

   - `pokemon/`: Lokale Pokemon-Daten (JSON)
   - `moves/`: Attacken-Daten
   - `species/`: Spezies-Informationen
   - `evolution-chains/`: Evolution-Ketten
   - `metadata/`: Index-Dateien für schnelle Lookups

3. **Build-Scripts** (`scripts/`)
   - `download-pokemon-data.js`: PokeAPI Daten herunterladen
   - `generate-indexes.js`: Index-Dateien generieren
   - `analyze-special-moves.js`: Spezial-Attacken analysieren

### Daten-Fluss

1. **Initialisierung**: Offline-Daten laden → API-Fallback bei Bedarf
2. **Pokemon-Erstellung**: Daten verarbeiten → Evolution prüfen → Moves filtern
3. **Kampf-Start**: Teams erstellen → Battle-System initialisieren
4. **Turn-Execution**: Aktionen sammeln → Priorität sortieren → Ausführen
5. **UI-Update**: Animationen → Status-Updates → Nächster Turn

## Deployment und Setup

### Lokale Entwicklung

```bash
# Daten herunterladen (optional, da bereits vorhanden)
npm run setup-offline

# Server starten
npm run serve
```

### Produktions-Deployment

- Statische Dateien auf Web-Server
- Keine Server-seitige Verarbeitung nötig
- Offline-Funktionalität durch lokale Daten

## Letzte Aktualisierung: 2025-07-22
