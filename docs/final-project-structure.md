# Pokemon Battle Game - Vollständige Projektdokumentation

## Überblick

Dieses Dokument beschreibt die vollständige Struktur und Funktionsweise des Pokemon Battle Games. Es dient als Referenz für zukünftige KI-Agents, die Änderungen am Projekt vornehmen oder die Architektur verstehen müssen.

**Projekt-Typ**: <PERSON>rowser-basiertes Pokemon-Kampfspiel  
**Technologie-Stack**: Vanilla JavaScript (ES6+), HTML5, CSS3  
**Daten-Quelle**: Lokale JSON-Dateien + PokeAPI Fallback  
**Zielgruppe**: Erste 151 Pokemon (Generation 1)  
**Deployment**: Statische Web-App (keine Server-Komponente erforderlich)

## Architektur-Übersicht

### Kern-Prinzipien

1. **Offline-First**: Vollständige Funktionalität ohne Internet-Verbindung
2. **Modulare Struktur**: Klare Trennung von Verantwortlichkeiten
3. **Authentizität**: Originalgetreue Pokemon-Mechaniken und Daten
4. **Performance**: Optimiert für Browser-Performance
5. **Responsive**: Mobile-First Design

### Haupt-Komponenten

```
pok-fgt/
├── index.html              # Haupt-HTML-Datei
├── css/styles.css          # Komplettes Styling
├── js/                     # JavaScript-Module
├── data/                   # Offline-Daten (JSON)
├── assets/                 # Bilder und Sprites
├── scripts/                # Build-Scripts
├── docs/                   # Dokumentation
└── tests/                  # Test-Dateien
```

## JavaScript-Module (js/)

### 1. config.js - Zentrale Konfiguration
**Zweck**: Alle Spiel-Konstanten und Konfigurationen  
**Wichtige Exports**:
- `CONFIG.BATTLE`: Kampf-Parameter (Level, Schaden, Status-Effekte)
- `CONFIG.TYPE_CHART`: 18x18 Typ-Effektivitäts-Matrix
- `CONFIG.AI`: KI-Schwierigkeitsgrade
- `CONFIG.TRAINERS`: Vordefinierte Trainer-Teams
- `calculateDamage()`: Globale Schadens-Berechnungs-Funktion

**Abhängigkeiten**: special-damage-calculator.js (optional)

### 2. pokemon.js - Pokemon-Klasse und Evolution
**Zweck**: Kern-Pokemon-Logik und Datenmodell  
**Haupt-Klasse**: `Pokemon`

**Wichtige Methoden**:
- `constructor(data, level)`: Pokemon-Initialisierung
- `calculateStats()`: Level-basierte Stat-Berechnung
- `selectMoves(moves)`: Level-angemessene Move-Auswahl
- `takeDamage(damage)`: Schadens-Verarbeitung
- `applyStatusEffect(status)`: Status-Effekt-Anwendung
- `processStatusEffects()`: Turn-basierte Status-Verarbeitung
- `reset()`: Pokemon für neuen Kampf zurücksetzen

**Evolution-Funktionen**:
- `getEvolutionChain(pokemonId)`: Evolution-Kette laden
- `getCorrectEvolutionForLevel(level, evolutionChain)`: Level-basierte Evolution
- `createPokemonWithEvolution(id, level)`: Pokemon mit korrekter Evolution erstellen

**Abhängigkeiten**: api.js, config.js

### 3. battle-system.js - Kampf-Mechaniken
**Zweck**: Turn-basiertes Kampfsystem  
**Haupt-Klasse**: `BattleSystem`

**Kern-Funktionalität**:
- Turn-Queue-Management mit Prioritäts-System
- Schadens-Berechnung mit Typ-Effektivität
- Status-Effekt-Verarbeitung
- Pokemon-Wechsel-Mechanik
- Sieg/Niederlage-Bedingungen

**Wichtige Methoden**:
- `startBattle()`: Kampf initialisieren
- `executeTurn()`: Turn-Ausführung
- `calculateDamage()`: Schadens-Kalkulation
- `applyTypeEffectiveness()`: Typ-Effektivität anwenden
- `checkBattleEnd()`: Kampf-Ende prüfen

**Abhängigkeiten**: pokemon.js, ai.js, ui.js, config.js

### 4. ai.js - KI-System
**Zweck**: Drei-stufiges KI-System für Gegner  
**Haupt-Klasse**: `PokemonAI`

**KI-Level**:
- **BASIC**: Zufällige Move-Auswahl
- **STANDARD**: Typ-Effektivität berücksichtigen
- **EXPERT**: Strategische Entscheidungen mit Schadens-Vorhersage

**Wichtige Methoden**:
- `selectAction()`: Haupt-Entscheidungs-Logik
- `evaluateMove()`: Move-Bewertung für Expert-AI
- `shouldSwitch()`: Pokemon-Wechsel-Entscheidung
- `selectBestSwitch()`: Bestes Wechsel-Pokemon finden

**Abhängigkeiten**: config.js

### 5. ui.js - UI-Management und Animationen
**Zweck**: Benutzeroberfläche und visuelle Effekte  
**Haupt-Klasse**: `UIManager`

**Funktionalitäten**:
- HP-Balken-Animationen
- Schadens-Zahlen-Anzeige
- Touch-Event-Handling
- Screen-Transitions
- Responsive Design-Anpassungen

**Wichtige Methoden**:
- `updatePokemonDisplay()`: Pokemon-Anzeige aktualisieren
- `animateHPBar()`: HP-Balken animieren
- `animateDamage()`: Schadens-Animation
- `showMessage()`: Text-Nachrichten anzeigen
- `handleResize()`: Responsive Anpassungen

**Abhängigkeiten**: config.js

### 6. api.js - Daten-Management
**Zweck**: PokeAPI Integration mit Caching  
**Haupt-Klasse**: `PokemonAPI`

**Funktionalitäten**:
- Rate-Limited API-Requests
- localStorage-Caching
- Offline-Daten-Integration
- Move-Daten-Verarbeitung
- Evolution-Chain-Loading

**Wichtige Methoden**:
- `fetchPokemon(id)`: Pokemon-Daten laden
- `loadPokemonData(id)`: Vollständige Daten-Verarbeitung
- `processPokemonMoves()`: Move-Daten aufbereiten
- `fetchEvolutionChain()`: Evolution-Daten laden
- `clearCache()`: Cache leeren

**Abhängigkeiten**: offline-data-loader.js, config.js

### 7. offline-data-loader.js - Lokale Daten
**Zweck**: Offline-Daten-Verwaltung  
**Haupt-Klasse**: `OfflineDataLoader`

**Funktionalitäten**:
- Lokale JSON-Dateien laden
- Index-basierte Lookups
- Fallback-Mechanismen
- Cache-Management

**Wichtige Methoden**:
- `loadPokemon(id)`: Pokemon aus lokalen Daten
- `loadMove(name)`: Move aus lokalen Daten
- `loadEvolutionChain(id)`: Evolution-Chain laden
- `checkOfflineDataAvailability()`: Verfügbarkeit prüfen

**Abhängigkeiten**: Keine

### 8. special-damage-calculator.js - Spezial-Attacken
**Zweck**: Attacken mit besonderen Schadens-Formeln  
**Haupt-Klasse**: `SpecialDamageCalculator`

**Unterstützte Formeln**:
- Fixed Damage (z.B. Seismic Toss)
- Level-based Damage (z.B. Night Shade)
- HP-Percentage Damage (z.B. Super Fang)
- Speed-based Damage (z.B. Electro Ball)
- OHKO Moves (z.B. Fissure)

**Wichtige Methoden**:
- `calculateSpecialDamage()`: Haupt-Berechnungs-Logik
- `hasSpecialFormula()`: Spezial-Formel prüfen
- Verschiedene `calculate*Damage()` Methoden für spezifische Formeln

**Abhängigkeiten**: Lokale JSON-Daten (special-moves.json)

### 9. main.js - Haupt-Controller
**Zweck**: Spiel-Initialisierung und Screen-Management  
**Haupt-Klasse**: `GameController`

**Funktionalitäten**:
- Screen-Navigation
- Team-Erstellung
- Trainer-Auswahl
- Spiel-Loop-Management

**Wichtige Methoden**:
- `init()`: Spiel initialisieren
- `showScreen()`: Screen wechseln
- `createPlayerTeam()`: Spieler-Team erstellen
- `startBattle()`: Kampf starten

**Abhängigkeiten**: Alle anderen Module
