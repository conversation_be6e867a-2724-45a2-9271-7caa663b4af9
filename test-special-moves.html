<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Special Moves Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-result {
            padding: 10px;
            margin: 5px 0;
            border-radius: 4px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .warning {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        #test-results {
            margin-top: 20px;
        }
        .move-test {
            border: 1px solid #ddd;
            margin: 10px 0;
            padding: 15px;
            border-radius: 8px;
        }
        .move-name {
            font-weight: bold;
            font-size: 1.2em;
            color: #333;
        }
    </style>
</head>
<body>
    <h1>Pokemon Game - Special Moves Test</h1>
    
    <div>
        <button onclick="testSpecialDamageCalculator()">Test Special Damage Calculator</button>
        <button onclick="testSpecialMoves()">Test Individual Special Moves</button>
        <button onclick="testBattleIntegration()">Test Battle System Integration</button>
        <button onclick="clearResults()">Clear Results</button>
    </div>

    <div id="test-results"></div>

    <!-- Include necessary scripts -->
    <script src="js/config.js"></script>
    <script src="js/offline-data-loader.js"></script>
    <script src="js/special-damage-calculator.js"></script>
    <script src="js/api.js"></script>
    <script src="js/pokemon.js"></script>
    <script src="js/battle-system.js"></script>

    <script>
        function addTestResult(message, type = 'success') {
            const resultsDiv = document.getElementById('test-results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result ${type}`;
            resultDiv.textContent = message;
            resultsDiv.appendChild(resultDiv);
            console.log(message);
        }

        function clearResults() {
            document.getElementById('test-results').innerHTML = '';
        }

        async function testSpecialDamageCalculator() {
            addTestResult('Testing Special Damage Calculator...', 'info');
            
            try {
                // Test if special damage calculator is available
                if (typeof specialDamageCalculator === 'undefined') {
                    addTestResult('Special damage calculator not available', 'error');
                    return;
                }

                addTestResult('✓ Special damage calculator is available');

                // Wait for data to load
                await new Promise(resolve => setTimeout(resolve, 1000));

                // Test if special move data is loaded
                if (!specialDamageCalculator.damageFormulas) {
                    addTestResult('⚠️ Special move data not loaded yet', 'warning');
                    return;
                }

                const formulaCount = Object.keys(specialDamageCalculator.damageFormulas).length;
                addTestResult(`✓ Special move formulas loaded: ${formulaCount} moves`);

                // List available special moves
                const specialMoves = Object.keys(specialDamageCalculator.damageFormulas);
                addTestResult(`📋 Special moves: ${specialMoves.join(', ')}`, 'info');

            } catch (error) {
                addTestResult(`✗ Special damage calculator test failed: ${error.message}`, 'error');
            }
        }

        async function testSpecialMoves() {
            addTestResult('Testing Individual Special Moves...', 'info');
            
            try {
                // Create test Pokemon
                const attacker = {
                    name: 'Machamp',
                    level: 50,
                    types: ['fighting'],
                    stats: {
                        attack: 130,
                        defense: 80,
                        specialAttack: 65,
                        specialDefense: 85,
                        speed: 55
                    },
                    currentHP: 150,
                    maxHP: 150
                };

                const defender = {
                    name: 'Alakazam',
                    level: 50,
                    types: ['psychic'],
                    stats: {
                        attack: 50,
                        defense: 45,
                        specialAttack: 135,
                        specialDefense: 95,
                        speed: 120
                    },
                    currentHP: 130,
                    maxHP: 130
                };

                // Test special moves
                const testMoves = [
                    { name: 'seismic-toss', type: 'fighting', damageClass: 'physical' },
                    { name: 'dragon-rage', type: 'dragon', damageClass: 'special' },
                    { name: 'sonic-boom', type: 'normal', damageClass: 'special' },
                    { name: 'night-shade', type: 'ghost', damageClass: 'special' },
                    { name: 'super-fang', type: 'normal', damageClass: 'physical' }
                ];

                for (const move of testMoves) {
                    const resultsDiv = document.getElementById('test-results');
                    const moveDiv = document.createElement('div');
                    moveDiv.className = 'move-test';
                    
                    const moveName = document.createElement('div');
                    moveName.className = 'move-name';
                    moveName.textContent = move.name.toUpperCase();
                    moveDiv.appendChild(moveName);

                    try {
                        if (specialDamageCalculator.hasSpecialFormula(move.name)) {
                            const result = specialDamageCalculator.calculateSpecialDamage(
                                attacker, defender, move, {}
                            );
                            
                            if (result) {
                                const damageInfo = document.createElement('div');
                                damageInfo.innerHTML = `
                                    <strong>Damage:</strong> ${result.damage}<br>
                                    <strong>Message:</strong> ${result.message}<br>
                                    <strong>Special:</strong> ${result.isSpecial ? 'Yes' : 'No'}
                                `;
                                moveDiv.appendChild(damageInfo);
                                
                                addTestResult(`✓ ${move.name}: ${result.damage} damage`);
                            } else {
                                addTestResult(`⚠️ ${move.name}: No result returned`, 'warning');
                            }
                        } else {
                            addTestResult(`⚠️ ${move.name}: No special formula found`, 'warning');
                        }
                    } catch (error) {
                        addTestResult(`✗ ${move.name}: Error - ${error.message}`, 'error');
                    }
                    
                    resultsDiv.appendChild(moveDiv);
                }

            } catch (error) {
                addTestResult(`✗ Special moves test failed: ${error.message}`, 'error');
            }
        }

        async function testBattleIntegration() {
            addTestResult('Testing Battle System Integration...', 'info');
            
            try {
                // Test if calculateDamage function works with special moves
                const attacker = {
                    name: 'Machamp',
                    level: 50,
                    types: ['fighting'],
                    stats: {
                        attack: 130,
                        defense: 80,
                        specialAttack: 65,
                        specialDefense: 85,
                        speed: 55
                    }
                };

                const defender = {
                    name: 'Alakazam',
                    level: 50,
                    types: ['psychic'],
                    stats: {
                        attack: 50,
                        defense: 45,
                        specialAttack: 135,
                        specialDefense: 95,
                        speed: 120
                    },
                    currentHP: 130,
                    maxHP: 130
                };

                // Test Seismic Toss (should deal damage equal to user's level)
                const seismicToss = { 
                    name: 'seismic-toss', 
                    type: 'fighting', 
                    damageClass: 'physical',
                    power: null
                };

                const damage = calculateDamage(attacker, defender, seismicToss, {});
                
                if (damage === attacker.level) {
                    addTestResult(`✓ Seismic Toss integration: ${damage} damage (equals level)`);
                } else {
                    addTestResult(`⚠️ Seismic Toss integration: ${damage} damage (expected ${attacker.level})`, 'warning');
                }

                // Test Dragon Rage (should deal fixed 40 damage)
                const dragonRage = { 
                    name: 'dragon-rage', 
                    type: 'dragon', 
                    damageClass: 'special',
                    power: null
                };

                const dragonDamage = calculateDamage(attacker, defender, dragonRage, {});
                
                if (dragonDamage === 40) {
                    addTestResult(`✓ Dragon Rage integration: ${dragonDamage} damage (fixed 40)`);
                } else {
                    addTestResult(`⚠️ Dragon Rage integration: ${dragonDamage} damage (expected 40)`, 'warning');
                }

                addTestResult('✓ Battle system integration test completed');

            } catch (error) {
                addTestResult(`✗ Battle integration test failed: ${error.message}`, 'error');
            }
        }

        // Initialize when page loads
        window.addEventListener('load', () => {
            addTestResult('🎮 Pokemon Game Special Moves Test Ready', 'info');
            addTestResult('Click the buttons above to test special move functionality', 'info');
            
            // Check if we're running from a web server
            if (location.protocol === 'file:') {
                addTestResult('⚠️ Running from file:// protocol - some features may not work. Please use a web server.', 'warning');
            } else {
                addTestResult('✓ Running from web server - all features should work');
            }
        });
    </script>
</body>
</html>
